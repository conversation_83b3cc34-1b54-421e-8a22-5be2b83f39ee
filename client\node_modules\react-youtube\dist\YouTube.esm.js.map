{"version": 3, "sources": ["../src/YouTube.tsx"], "sourcesContent": ["/** @jsxRuntime classic */\nimport PropTypes from 'prop-types';\nimport React from 'react';\nimport isEqual from 'fast-deep-equal';\nimport youTubePlayer from 'youtube-player';\nimport type { YouTubePlayer, Options } from 'youtube-player/dist/types';\n\n/**\n * Check whether a `props` change should result in the video being updated.\n */\nfunction shouldUpdateVideo(prevProps: YouTubeProps, props: YouTubeProps) {\n  // A changing video should always trigger an update\n  if (prevProps.videoId !== props.videoId) {\n    return true;\n  }\n\n  // Otherwise, a change in the start/end time playerVars also requires a player\n  // update.\n  const prevVars = prevProps.opts?.playerVars || {};\n  const vars = props.opts?.playerVars || {};\n\n  return prevVars.start !== vars.start || prevVars.end !== vars.end;\n}\n\n/**\n * Neutralize API options that only require a video update, leaving only options\n * that require a player reset. The results can then be compared to see if a\n * player reset is necessary.\n */\nfunction filterResetOptions(opts: Options = {}) {\n  return {\n    ...opts,\n    height: 0,\n    width: 0,\n    playerVars: {\n      ...opts.playerVars,\n      autoplay: 0,\n      start: 0,\n      end: 0,\n    },\n  };\n}\n\n/**\n * Check whether a `props` change should result in the player being reset.\n * The player is reset when the `props.opts` change, except if the only change\n * is in the `start` and `end` playerVars, because a video update can deal with\n * those.\n */\nfunction shouldResetPlayer(prevProps: YouTubeProps, props: YouTubeProps) {\n  return (\n    prevProps.videoId !== props.videoId || !isEqual(filterResetOptions(prevProps.opts), filterResetOptions(props.opts))\n  );\n}\n\n/**\n * Check whether a props change should result in an update of player.\n */\nfunction shouldUpdatePlayer(prevProps: YouTubeProps, props: YouTubeProps) {\n  return (\n    prevProps.id !== props.id ||\n    prevProps.className !== props.className ||\n    prevProps.opts?.width !== props.opts?.width ||\n    prevProps.opts?.height !== props.opts?.height ||\n    prevProps.iframeClassName !== props.iframeClassName ||\n    prevProps.title !== props.title\n  );\n}\n\ntype YoutubePlayerCueVideoOptions = {\n  videoId: string;\n  startSeconds?: number;\n  endSeconds?: number;\n  suggestedQuality?: string;\n};\n\nexport { YouTubePlayer };\n\nexport type YouTubeEvent<T = any> = {\n  data: T;\n  target: YouTubePlayer;\n};\n\nexport type YouTubeProps = {\n  /**\n   * The YouTube video ID.\n   *\n   * Examples\n   * - https://www.youtube.com/watch?v=XxVg_s8xAms (`XxVg_s8xAms` is the ID)\n   * - https://www.youtube.com/embed/-DX3vJiqxm4 (`-DX3vJiqxm4` is the ID)\n   */\n  videoId?: string;\n  /**\n   * Custom ID for the player element\n   */\n  id?: string;\n  /**\n   * Custom class name for the player element\n   */\n  className?: string;\n  /**\n   * Custom class name for the iframe element\n   */\n  iframeClassName?: string;\n  /**\n   * Custom style for the player container element\n   */\n  style?: React.CSSProperties;\n  /**\n   * Title of the video for the iframe's title tag.\n   */\n  title?: React.IframeHTMLAttributes<HTMLIFrameElement>['title'];\n  /**\n   * Indicates how the browser should load the iframe\n   * {@link https://developer.mozilla.org/en-US/docs/Web/HTML/Element/iframe#attr-loading}\n   */\n  loading?: React.IframeHTMLAttributes<HTMLIFrameElement>['loading'];\n  /**\n   * An object that specifies player options\n   * {@link https://developers.google.com/youtube/iframe_api_reference#Loading_a_Video_Player}\n   */\n  opts?: Options;\n  /**\n   * This event fires whenever a player has finished loading and is ready to begin receiving API calls.\n   * {@link https://developers.google.com/youtube/iframe_api_reference#onReady}\n   */\n  onReady?: (event: YouTubeEvent) => void;\n  /**\n   * This event fires if an error occurs in the player.\n   * {@link https://developers.google.com/youtube/iframe_api_reference#onError}\n   */\n  onError?: (event: YouTubeEvent<number>) => void;\n  /**\n   * This event fires when the layer's state changes to PlayerState.PLAYING.\n   */\n  onPlay?: (event: YouTubeEvent<number>) => void;\n  /**\n   * This event fires when the layer's state changes to PlayerState.PAUSED.\n   */\n  onPause?: (event: YouTubeEvent<number>) => void;\n  /**\n   * This event fires when the layer's state changes to PlayerState.ENDED.\n   */\n  onEnd?: (event: YouTubeEvent<number>) => void;\n  /**\n   * This event fires whenever the player's state changes.\n   * {@link https://developers.google.com/youtube/iframe_api_reference#onStateChange}\n   */\n  onStateChange?: (event: YouTubeEvent<number>) => void;\n  /**\n   * This event fires whenever the video playback quality changes.\n   * {@link https://developers.google.com/youtube/iframe_api_reference#onPlaybackRateChange}\n   */\n  onPlaybackRateChange?: (event: YouTubeEvent<number>) => void;\n  /**\n   * This event fires whenever the video playback rate changes.\n   * {@link https://developers.google.com/youtube/iframe_api_reference#onPlaybackQualityChange}\n   */\n  onPlaybackQualityChange?: (event: YouTubeEvent<string>) => void;\n};\n\nconst defaultProps: YouTubeProps = {\n  videoId: '',\n  id: '',\n  className: '',\n  iframeClassName: '',\n  style: {},\n  title: '',\n  loading: undefined,\n  opts: {},\n  onReady: () => {},\n  onError: () => {},\n  onPlay: () => {},\n  onPause: () => {},\n  onEnd: () => {},\n  onStateChange: () => {},\n  onPlaybackRateChange: () => {},\n  onPlaybackQualityChange: () => {},\n};\n\nconst propTypes = {\n  videoId: PropTypes.string,\n  id: PropTypes.string,\n  className: PropTypes.string,\n  iframeClassName: PropTypes.string,\n  style: PropTypes.object,\n  title: PropTypes.string,\n  loading: PropTypes.oneOf(['lazy', 'eager']),\n  opts: PropTypes.objectOf(PropTypes.any),\n  onReady: PropTypes.func,\n  onError: PropTypes.func,\n  onPlay: PropTypes.func,\n  onPause: PropTypes.func,\n  onEnd: PropTypes.func,\n  onStateChange: PropTypes.func,\n  onPlaybackRateChange: PropTypes.func,\n  onPlaybackQualityChange: PropTypes.func,\n};\n\nclass YouTube extends React.Component<YouTubeProps> {\n  static propTypes = propTypes;\n  static defaultProps = defaultProps;\n\n  /**\n   * Expose PlayerState constants for convenience. These constants can also be\n   * accessed through the global YT object after the YouTube IFrame API is instantiated.\n   * https://developers.google.com/youtube/iframe_api_reference#onStateChange\n   */\n  static PlayerState = {\n    UNSTARTED: -1,\n    ENDED: 0,\n    PLAYING: 1,\n    PAUSED: 2,\n    BUFFERING: 3,\n    CUED: 5,\n  };\n\n  container: HTMLDivElement | null;\n  internalPlayer: YouTubePlayer | null;\n\n  constructor(props: any) {\n    super(props);\n\n    this.container = null;\n    this.internalPlayer = null;\n  }\n\n  /**\n   * Note: The `youtube-player` package that is used promisifies all YouTube\n   * Player API calls, which introduces a delay of a tick before it actually\n   * gets destroyed.\n   *\n   * The promise to destroy the player is stored here so we can make sure to\n   * only re-create the Player after it's been destroyed.\n   *\n   * See: https://github.com/tjallingt/react-youtube/issues/355\n   */\n  destroyPlayerPromise: Promise<void> | undefined = undefined;\n\n  componentDidMount() {\n    this.createPlayer();\n  }\n\n  async componentDidUpdate(prevProps: YouTubeProps) {\n    if (shouldUpdatePlayer(prevProps, this.props)) {\n      this.updatePlayer();\n    }\n\n    if (shouldResetPlayer(prevProps, this.props)) {\n      await this.resetPlayer();\n    }\n\n    if (shouldUpdateVideo(prevProps, this.props)) {\n      this.updateVideo();\n    }\n  }\n\n  componentWillUnmount() {\n    this.destroyPlayer();\n  }\n\n  /**\n   * This event fires whenever a player has finished loading and is ready to begin receiving API calls.\n   * https://developers.google.com/youtube/iframe_api_reference#onReady\n   */\n  onPlayerReady = (event: YouTubeEvent) => this.props.onReady?.(event);\n\n  /**\n   * This event fires if an error occurs in the player.\n   * https://developers.google.com/youtube/iframe_api_reference#onError\n   */\n  onPlayerError = (event: YouTubeEvent<number>) => this.props.onError?.(event);\n\n  /**\n   * This event fires whenever the video playback quality changes.\n   * https://developers.google.com/youtube/iframe_api_reference#onStateChange\n   */\n  onPlayerStateChange = (event: YouTubeEvent<number>) => {\n    this.props.onStateChange?.(event);\n    // @ts-ignore\n    switch (event.data) {\n      case YouTube.PlayerState.ENDED:\n        this.props.onEnd?.(event);\n        break;\n\n      case YouTube.PlayerState.PLAYING:\n        this.props.onPlay?.(event);\n        break;\n\n      case YouTube.PlayerState.PAUSED:\n        this.props.onPause?.(event);\n        break;\n\n      default:\n    }\n  };\n\n  /**\n   * This event fires whenever the video playback quality changes.\n   * https://developers.google.com/youtube/iframe_api_reference#onPlaybackRateChange\n   */\n  onPlayerPlaybackRateChange = (event: YouTubeEvent<number>) => this.props.onPlaybackRateChange?.(event);\n\n  /**\n   * This event fires whenever the video playback rate changes.\n   * https://developers.google.com/youtube/iframe_api_reference#onPlaybackQualityChange\n   */\n  onPlayerPlaybackQualityChange = (event: YouTubeEvent<string>) => this.props.onPlaybackQualityChange?.(event);\n\n  /**\n   * Destroy the YouTube Player using its async API and store the promise so we\n   * can await before re-creating it.\n   */\n  destroyPlayer = () => {\n    if (this.internalPlayer) {\n      this.destroyPlayerPromise = this.internalPlayer.destroy().then(() => (this.destroyPlayerPromise = undefined));\n      return this.destroyPlayerPromise;\n    }\n    return Promise.resolve();\n  };\n\n  /**\n   * Initialize the YouTube Player API on the container and attach event handlers\n   */\n  createPlayer = () => {\n    // do not attempt to create a player server-side, it won't work\n    if (typeof document === 'undefined') return;\n    if (this.destroyPlayerPromise) {\n      // We need to first await the existing player to be destroyed before\n      // we can re-create it.\n      this.destroyPlayerPromise.then(this.createPlayer);\n      return;\n    }\n    // create player\n    const playerOpts: Options = {\n      ...this.props.opts,\n      // preload the `videoId` video if one is already given\n      videoId: this.props.videoId,\n    };\n    this.internalPlayer = youTubePlayer(this.container!, playerOpts);\n    // attach event handlers\n    this.internalPlayer.on('ready', this.onPlayerReady as any);\n    this.internalPlayer.on('error', this.onPlayerError as any);\n    this.internalPlayer.on('stateChange', this.onPlayerStateChange as any);\n    this.internalPlayer.on('playbackRateChange', this.onPlayerPlaybackRateChange as any);\n    this.internalPlayer.on('playbackQualityChange', this.onPlayerPlaybackQualityChange as any);\n    if (this.props.title || this.props.loading) {\n      this.internalPlayer.getIframe().then((iframe) => {\n        if (this.props.title) iframe.setAttribute('title', this.props.title);\n        if (this.props.loading) iframe.setAttribute('loading', this.props.loading);\n      });\n    }\n  };\n\n  /**\n   * Shorthand for destroying and then re-creating the YouTube Player\n   */\n  resetPlayer = () => this.destroyPlayer().then(this.createPlayer);\n\n  /**\n   * Method to update the id and class of the YouTube Player iframe.\n   * React should update this automatically but since the YouTube Player API\n   * replaced the DIV that is mounted by React we need to do this manually.\n   */\n  updatePlayer = () => {\n    this.internalPlayer?.getIframe().then((iframe) => {\n      if (this.props.id) iframe.setAttribute('id', this.props.id);\n      else iframe.removeAttribute('id');\n      if (this.props.iframeClassName) iframe.setAttribute('class', this.props.iframeClassName);\n      else iframe.removeAttribute('class');\n      if (this.props.opts && this.props.opts.width) iframe.setAttribute('width', this.props.opts.width.toString());\n      else iframe.removeAttribute('width');\n      if (this.props.opts && this.props.opts.height) iframe.setAttribute('height', this.props.opts.height.toString());\n      else iframe.removeAttribute('height');\n      if (this.props.title) iframe.setAttribute('title', this.props.title);\n      else iframe.setAttribute('title', 'YouTube video player');\n      if (this.props.loading) iframe.setAttribute('loading', this.props.loading);\n      else iframe.removeAttribute('loading');\n    });\n  };\n\n  /**\n   *  Method to return the internalPlayer object.\n   */\n  getInternalPlayer = () => {\n    return this.internalPlayer;\n  };\n\n  /**\n   * Call YouTube Player API methods to update the currently playing video.\n   * Depending on the `opts.playerVars.autoplay` this function uses one of two\n   * YouTube Player API methods to update the video.\n   */\n  updateVideo = () => {\n    if (typeof this.props.videoId === 'undefined' || this.props.videoId === null) {\n      this.internalPlayer?.stopVideo();\n      return;\n    }\n\n    // set queueing options\n    let autoplay = false;\n    const opts: YoutubePlayerCueVideoOptions = {\n      videoId: this.props.videoId,\n    };\n\n    if (this.props.opts?.playerVars) {\n      autoplay = this.props.opts.playerVars.autoplay === 1;\n      if ('start' in this.props.opts.playerVars) {\n        opts.startSeconds = this.props.opts.playerVars.start;\n      }\n      if ('end' in this.props.opts.playerVars) {\n        opts.endSeconds = this.props.opts.playerVars.end;\n      }\n    }\n\n    // if autoplay is enabled loadVideoById\n    if (autoplay) {\n      this.internalPlayer?.loadVideoById(opts);\n      return;\n    }\n    // default behaviour just cues the video\n    this.internalPlayer?.cueVideoById(opts);\n  };\n\n  refContainer = (container: HTMLDivElement) => {\n    this.container = container;\n  };\n\n  render() {\n    return (\n      <div className={this.props.className} style={this.props.style}>\n        <div id={this.props.id} className={this.props.iframeClassName} ref={this.refContainer} />\n      </div>\n    );\n  }\n}\n\nexport default YouTube;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,OAAO,eAAe;AACtB,OAAO,WAAW;AAClB,OAAO,aAAa;AACpB,OAAO,mBAAmB;AAM1B,SAAS,kBAAkB,WAAyB,OAAqB;AAVzE;AAYE,MAAI,UAAU,YAAY,MAAM,SAAS;AACvC,WAAO;AAAA,EACT;AAIA,QAAM,aAAW,eAAU,SAAV,mBAAgB,eAAc,CAAC;AAChD,QAAM,SAAO,WAAM,SAAN,mBAAY,eAAc,CAAC;AAExC,SAAO,SAAS,UAAU,KAAK,SAAS,SAAS,QAAQ,KAAK;AAChE;AAOA,SAAS,mBAAmB,OAAgB,CAAC,GAAG;AAC9C,SAAO,iCACF,OADE;AAAA,IAEL,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,YAAY,iCACP,KAAK,aADE;AAAA,MAEV,UAAU;AAAA,MACV,OAAO;AAAA,MACP,KAAK;AAAA,IACP;AAAA,EACF;AACF;AAQA,SAAS,kBAAkB,WAAyB,OAAqB;AACvE,SACE,UAAU,YAAY,MAAM,WAAW,CAAC,QAAQ,mBAAmB,UAAU,IAAI,GAAG,mBAAmB,MAAM,IAAI,CAAC;AAEtH;AAKA,SAAS,mBAAmB,WAAyB,OAAqB;AA1D1E;AA2DE,SACE,UAAU,OAAO,MAAM,MACvB,UAAU,cAAc,MAAM,eAC9B,eAAU,SAAV,mBAAgB,aAAU,WAAM,SAAN,mBAAY,YACtC,eAAU,SAAV,mBAAgB,cAAW,WAAM,SAAN,mBAAY,WACvC,UAAU,oBAAoB,MAAM,mBACpC,UAAU,UAAU,MAAM;AAE9B;AA8FA,IAAM,eAA6B;AAAA,EACjC,SAAS;AAAA,EACT,IAAI;AAAA,EACJ,WAAW;AAAA,EACX,iBAAiB;AAAA,EACjB,OAAO,CAAC;AAAA,EACR,OAAO;AAAA,EACP,SAAS;AAAA,EACT,MAAM,CAAC;AAAA,EACP,SAAS,MAAM;AAAA,EAAC;AAAA,EAChB,SAAS,MAAM;AAAA,EAAC;AAAA,EAChB,QAAQ,MAAM;AAAA,EAAC;AAAA,EACf,SAAS,MAAM;AAAA,EAAC;AAAA,EAChB,OAAO,MAAM;AAAA,EAAC;AAAA,EACd,eAAe,MAAM;AAAA,EAAC;AAAA,EACtB,sBAAsB,MAAM;AAAA,EAAC;AAAA,EAC7B,yBAAyB,MAAM;AAAA,EAAC;AAClC;AAEA,IAAM,YAAY;AAAA,EAChB,SAAS,UAAU;AAAA,EACnB,IAAI,UAAU;AAAA,EACd,WAAW,UAAU;AAAA,EACrB,iBAAiB,UAAU;AAAA,EAC3B,OAAO,UAAU;AAAA,EACjB,OAAO,UAAU;AAAA,EACjB,SAAS,UAAU,MAAM,CAAC,QAAQ,OAAO,CAAC;AAAA,EAC1C,MAAM,UAAU,SAAS,UAAU,GAAG;AAAA,EACtC,SAAS,UAAU;AAAA,EACnB,SAAS,UAAU;AAAA,EACnB,QAAQ,UAAU;AAAA,EAClB,SAAS,UAAU;AAAA,EACnB,OAAO,UAAU;AAAA,EACjB,eAAe,UAAU;AAAA,EACzB,sBAAsB,UAAU;AAAA,EAChC,yBAAyB,UAAU;AACrC;AAEA,IAAM,WAAN,cAAsB,MAAM,UAAwB;AAAA,EAqBlD,YAAY,OAAY;AACtB,UAAM,KAAK;AAgBb,gCAAkD;AA4BlD,yBAAgB,CAAC,UAAqB;AAzQxC;AAyQ2C,8BAAK,OAAM,YAAX,4BAAqB;AAAA;AAM9D,yBAAgB,CAAC,UAA6B;AA/QhD;AA+QmD,8BAAK,OAAM,YAAX,4BAAqB;AAAA;AAMtE,+BAAsB,CAAC,UAAgC;AArRzD;AAsRI,uBAAK,OAAM,kBAAX,4BAA2B;AAE3B,cAAQ,MAAM;AAAA,aACP,SAAQ,YAAY;AACvB,2BAAK,OAAM,UAAX,4BAAmB;AACnB;AAAA,aAEG,SAAQ,YAAY;AACvB,2BAAK,OAAM,WAAX,4BAAoB;AACpB;AAAA,aAEG,SAAQ,YAAY;AACvB,2BAAK,OAAM,YAAX,4BAAqB;AACrB;AAAA;AAAA;AAAA,IAIN;AAMA,sCAA6B,CAAC,UAA6B;AA7S7D;AA6SgE,8BAAK,OAAM,yBAAX,4BAAkC;AAAA;AAMhG,yCAAgC,CAAC,UAA6B;AAnThE;AAmTmE,8BAAK,OAAM,4BAAX,4BAAqC;AAAA;AAMtG,yBAAgB,MAAM;AACpB,UAAI,KAAK,gBAAgB;AACvB,aAAK,uBAAuB,KAAK,eAAe,QAAQ,EAAE,KAAK,MAAO,KAAK,uBAAuB,MAAU;AAC5G,eAAO,KAAK;AAAA,MACd;AACA,aAAO,QAAQ,QAAQ;AAAA,IACzB;AAKA,wBAAe,MAAM;AAEnB,UAAI,OAAO,aAAa;AAAa;AACrC,UAAI,KAAK,sBAAsB;AAG7B,aAAK,qBAAqB,KAAK,KAAK,YAAY;AAChD;AAAA,MACF;AAEA,YAAM,aAAsB,iCACvB,KAAK,MAAM,OADY;AAAA,QAG1B,SAAS,KAAK,MAAM;AAAA,MACtB;AACA,WAAK,iBAAiB,cAAc,KAAK,WAAY,UAAU;AAE/D,WAAK,eAAe,GAAG,SAAS,KAAK,aAAoB;AACzD,WAAK,eAAe,GAAG,SAAS,KAAK,aAAoB;AACzD,WAAK,eAAe,GAAG,eAAe,KAAK,mBAA0B;AACrE,WAAK,eAAe,GAAG,sBAAsB,KAAK,0BAAiC;AACnF,WAAK,eAAe,GAAG,yBAAyB,KAAK,6BAAoC;AACzF,UAAI,KAAK,MAAM,SAAS,KAAK,MAAM,SAAS;AAC1C,aAAK,eAAe,UAAU,EAAE,KAAK,CAAC,WAAW;AAC/C,cAAI,KAAK,MAAM;AAAO,mBAAO,aAAa,SAAS,KAAK,MAAM,KAAK;AACnE,cAAI,KAAK,MAAM;AAAS,mBAAO,aAAa,WAAW,KAAK,MAAM,OAAO;AAAA,QAC3E,CAAC;AAAA,MACH;AAAA,IACF;AAKA,uBAAc,MAAM,KAAK,cAAc,EAAE,KAAK,KAAK,YAAY;AAO/D,wBAAe,MAAM;AA5WvB;AA6WI,iBAAK,mBAAL,mBAAqB,YAAY,KAAK,CAAC,WAAW;AAChD,YAAI,KAAK,MAAM;AAAI,iBAAO,aAAa,MAAM,KAAK,MAAM,EAAE;AAAA;AACrD,iBAAO,gBAAgB,IAAI;AAChC,YAAI,KAAK,MAAM;AAAiB,iBAAO,aAAa,SAAS,KAAK,MAAM,eAAe;AAAA;AAClF,iBAAO,gBAAgB,OAAO;AACnC,YAAI,KAAK,MAAM,QAAQ,KAAK,MAAM,KAAK;AAAO,iBAAO,aAAa,SAAS,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAAA;AACtG,iBAAO,gBAAgB,OAAO;AACnC,YAAI,KAAK,MAAM,QAAQ,KAAK,MAAM,KAAK;AAAQ,iBAAO,aAAa,UAAU,KAAK,MAAM,KAAK,OAAO,SAAS,CAAC;AAAA;AACzG,iBAAO,gBAAgB,QAAQ;AACpC,YAAI,KAAK,MAAM;AAAO,iBAAO,aAAa,SAAS,KAAK,MAAM,KAAK;AAAA;AAC9D,iBAAO,aAAa,SAAS,sBAAsB;AACxD,YAAI,KAAK,MAAM;AAAS,iBAAO,aAAa,WAAW,KAAK,MAAM,OAAO;AAAA;AACpE,iBAAO,gBAAgB,SAAS;AAAA,MACvC;AAAA,IACF;AAKA,6BAAoB,MAAM;AACxB,aAAO,KAAK;AAAA,IACd;AAOA,uBAAc,MAAM;AAzYtB;AA0YI,UAAI,OAAO,KAAK,MAAM,YAAY,eAAe,KAAK,MAAM,YAAY,MAAM;AAC5E,mBAAK,mBAAL,mBAAqB;AACrB;AAAA,MACF;AAGA,UAAI,WAAW;AACf,YAAM,OAAqC;AAAA,QACzC,SAAS,KAAK,MAAM;AAAA,MACtB;AAEA,WAAI,UAAK,MAAM,SAAX,mBAAiB,YAAY;AAC/B,mBAAW,KAAK,MAAM,KAAK,WAAW,aAAa;AACnD,YAAI,WAAW,KAAK,MAAM,KAAK,YAAY;AACzC,eAAK,eAAe,KAAK,MAAM,KAAK,WAAW;AAAA,QACjD;AACA,YAAI,SAAS,KAAK,MAAM,KAAK,YAAY;AACvC,eAAK,aAAa,KAAK,MAAM,KAAK,WAAW;AAAA,QAC/C;AAAA,MACF;AAGA,UAAI,UAAU;AACZ,mBAAK,mBAAL,mBAAqB,cAAc;AACnC;AAAA,MACF;AAEA,iBAAK,mBAAL,mBAAqB,aAAa;AAAA,IACpC;AAEA,wBAAe,CAAC,cAA8B;AAC5C,WAAK,YAAY;AAAA,IACnB;AA3ME,SAAK,YAAY;AACjB,SAAK,iBAAiB;AAAA,EACxB;AAAA,EAcA,oBAAoB;AAClB,SAAK,aAAa;AAAA,EACpB;AAAA,EAEM,mBAAmB,WAAyB;AAAA;AAChD,UAAI,mBAAmB,WAAW,KAAK,KAAK,GAAG;AAC7C,aAAK,aAAa;AAAA,MACpB;AAEA,UAAI,kBAAkB,WAAW,KAAK,KAAK,GAAG;AAC5C,cAAM,KAAK,YAAY;AAAA,MACzB;AAEA,UAAI,kBAAkB,WAAW,KAAK,KAAK,GAAG;AAC5C,aAAK,YAAY;AAAA,MACnB;AAAA,IACF;AAAA;AAAA,EAEA,uBAAuB;AACrB,SAAK,cAAc;AAAA,EACrB;AAAA,EAyKA,SAAS;AACP,WACE,oCAAC;AAAA,MAAI,WAAW,KAAK,MAAM;AAAA,MAAW,OAAO,KAAK,MAAM;AAAA,OACtD,oCAAC;AAAA,MAAI,IAAI,KAAK,MAAM;AAAA,MAAI,WAAW,KAAK,MAAM;AAAA,MAAiB,KAAK,KAAK;AAAA,KAAc,CACzF;AAAA,EAEJ;AACF;AA5OA,IAAM,UAAN;AAAM,QACG,YAAY;AADf,QAEG,eAAe;AAFlB,QASG,cAAc;AAAA,EACnB,WAAW;AAAA,EACX,OAAO;AAAA,EACP,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,MAAM;AACR;AA8NF,IAAO,kBAAQ;", "names": []}