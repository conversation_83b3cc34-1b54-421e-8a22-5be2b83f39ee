# YouTube Player Integration

This implementation adds YouTube video support to the LMS using the YouTube Data API v3 and react-youtube library.

## Features

- **YouTube Video Player**: Embedded YouTube player with custom controls
- **Multiple URL Formats**: Supports standard YouTube URLs, short URLs (youtu.be), embed URLs, and video IDs
- **Video Information**: Displays video title, view count, likes, and publish date using YouTube API
- **Custom Controls**: Additional playback controls (play, pause, forward/backward 10 seconds)
- **Loading States**: Shows loading spinner while video information is being fetched
- **Error Handling**: Graceful error handling for invalid videos or API failures
- **Responsive Design**: Works on desktop and mobile devices
- **Dark Mode Support**: Supports both light and dark themes

## Components

### YouTubePlayer
Main component for rendering YouTube videos with enhanced features.

**Props:**
- `videoId` (string): YouTube video ID
- `title` (string, optional): Video title for accessibility

**Features:**
- Fetches video metadata using YouTube Data API v3
- Custom playback controls
- Video information display
- Loading and error states

### YouTubePlayerDemo
Demo component showing how to use the YouTube player with different URL formats.

## Utilities

### youtubeUtils.js
Utility functions for YouTube URL handling:

- `extractYouTubeVideoId(url)`: Extracts video ID from various YouTube URL formats
- `isYouTubeUrl(url)`: Checks if a URL is a YouTube URL
- `getYouTubeThumbnail(videoId, quality)`: Generates thumbnail URLs

## Configuration

### youtube.js
Configuration file containing:
- YouTube Data API v3 key
- API endpoints
- Default player options

**Important**: In production, store the API key in environment variables:
```javascript
API_KEY: import.meta.env.VITE_YOUTUBE_API_KEY
```

## Integration with LMS

The YouTube player is integrated into the `DisplayLecture` component:

1. **Video Detection**: Automatically detects YouTube URLs in lecture data
2. **Fallback Support**: Falls back to uploaded video files if no YouTube URL
3. **Seamless Integration**: Maintains existing UI/UX while adding YouTube support

## Supported URL Formats

- Standard: `https://www.youtube.com/watch?v=VIDEO_ID`
- Short: `https://youtu.be/VIDEO_ID`
- Embed: `https://www.youtube.com/embed/VIDEO_ID`
- Video ID only: `VIDEO_ID`

## API Usage

The implementation uses YouTube Data API v3 to fetch:
- Video title and description
- View count and like count
- Publish date
- Channel information

**API Endpoint Used:**
```
https://www.googleapis.com/youtube/v3/videos?part=snippet,statistics&id=VIDEO_ID&key=API_KEY
```

## Installation

1. Install react-youtube:
```bash
npm install react-youtube
```

2. Add your YouTube Data API v3 key to the configuration
3. Import and use the YouTubePlayer component

## Usage Example

```jsx
import YouTubePlayer from './Components/YouTubePlayer';

function MyComponent() {
  return (
    <YouTubePlayer 
      videoId="dQw4w9WgXcQ" 
      title="Sample Video"
    />
  );
}
```

## Security Notes

- API key is currently hardcoded for demo purposes
- In production, use environment variables
- Consider implementing API key rotation
- Monitor API usage to stay within quotas

## Browser Support

- Modern browsers with ES6+ support
- YouTube iframe API support required
- JavaScript must be enabled

## Troubleshooting

1. **Video not loading**: Check if video ID is valid and video is public
2. **API errors**: Verify API key is correct and has YouTube Data API v3 enabled
3. **Controls not working**: Ensure video has finished loading (onReady event)

## Future Enhancements

- Playlist support
- Video quality selection
- Playback speed controls
- Subtitle/caption support
- Video analytics integration
