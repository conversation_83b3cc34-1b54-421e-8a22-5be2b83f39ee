import React, { useState } from 'react';
import YouTubePlayer from './YouTubePlayer';
import { extractYouTubeVideoId } from '../utils/youtubeUtils';

const YouTubePlayerDemo = () => {
  const [videoUrl, setVideoUrl] = useState('');
  const [currentVideoId, setCurrentVideoId] = useState('');

  const handleUrlSubmit = (e) => {
    e.preventDefault();
    const videoId = extractYouTubeVideoId(videoUrl);
    if (videoId) {
      setCurrentVideoId(videoId);
    } else {
      alert('Invalid YouTube URL. Please enter a valid YouTube URL.');
    }
  };

  const sampleVideos = [
    {
      id: 'dQw4w9WgXcQ',
      title: '<PERSON> - Never Gonna Give You Up',
      url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ'
    },
    {
      id: 'jNQXAC9IVRw',
      title: 'Me at the zoo',
      url: 'https://www.youtube.com/watch?v=jNQXAC9IVRw'
    }
  ];

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-3xl font-bold text-center mb-8 text-gray-800 dark:text-white">
        YouTube Player Demo
      </h1>

      {/* URL Input Form */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
        <h2 className="text-xl font-semibold mb-4 text-gray-700 dark:text-gray-300">
          Enter YouTube URL
        </h2>
        <form onSubmit={handleUrlSubmit} className="flex gap-4">
          <input
            type="text"
            value={videoUrl}
            onChange={(e) => setVideoUrl(e.target.value)}
            placeholder="https://www.youtube.com/watch?v=VIDEO_ID"
            className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
          />
          <button
            type="submit"
            className="px-6 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md font-medium transition-colors"
          >
            Load Video
          </button>
        </form>
      </div>

      {/* Sample Videos */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
        <h2 className="text-xl font-semibold mb-4 text-gray-700 dark:text-gray-300">
          Sample Videos
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {sampleVideos.map((video) => (
            <button
              key={video.id}
              onClick={() => setCurrentVideoId(video.id)}
              className="p-4 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 text-left transition-colors"
            >
              <h3 className="font-medium text-gray-800 dark:text-gray-200">{video.title}</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{video.url}</p>
            </button>
          ))}
        </div>
      </div>

      {/* YouTube Player */}
      {currentVideoId && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <h2 className="text-xl font-semibold mb-4 text-gray-700 dark:text-gray-300">
            Now Playing: {currentVideoId}
          </h2>
          <YouTubePlayer videoId={currentVideoId} />
        </div>
      )}

      {/* Instructions */}
      <div className="bg-blue-50 dark:bg-blue-900 rounded-lg p-6 mt-8">
        <h2 className="text-xl font-semibold mb-4 text-blue-800 dark:text-blue-200">
          How to Use
        </h2>
        <ul className="list-disc list-inside space-y-2 text-blue-700 dark:text-blue-300">
          <li>Enter any YouTube URL in the input field above</li>
          <li>Supported formats: youtube.com/watch?v=ID, youtu.be/ID, youtube.com/embed/ID</li>
          <li>Use the custom controls below the video for enhanced playback control</li>
          <li>Video information is fetched using the YouTube Data API v3</li>
          <li>The player supports all standard YouTube features</li>
        </ul>
      </div>
    </div>
  );
};

export default YouTubePlayerDemo;
