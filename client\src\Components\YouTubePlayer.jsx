import React, { useRef, useState } from "react";
import YouTube from "react-youtube";

const YouTubePlayer = ({ videoId, title = "YouTube Video" }) => {
  const playerRef = useRef(null);
  const [isReady, setIsReady] = useState(false);

  const onPlayerReady = (event) => {
    playerRef.current = event.target;
    setIsReady(true);
  };

  const onPlayerError = (event) => {
    console.error('YouTube Player Error:', event.data);
  };

  const playVideo = () => {
    if (playerRef.current && isReady) {
      playerRef.current.playVideo();
    }
  };

  const pauseVideo = () => {
    if (playerRef.current && isReady) {
      playerRef.current.pauseVideo();
    }
  };

  const forwardVideo = () => {
    if (playerRef.current && isReady) {
      const currentTime = playerRef.current.getCurrentTime();
      playerRef.current.seekTo(currentTime + 10, true); // Forward 10 seconds
    }
  };

  const backwardVideo = () => {
    if (playerRef.current && isReady) {
      const currentTime = playerRef.current.getCurrentTime();
      playerRef.current.seekTo(Math.max(0, currentTime - 10), true); // Backward 10 seconds
    }
  };

  const opts = {
    height: "170",
    width: "100%",
    playerVars: {
      autoplay: 0,
      controls: 1,
      rel: 0,
      showinfo: 0,
      modestbranding: 1,
      fs: 1,
      cc_load_policy: 0,
      iv_load_policy: 3,
      autohide: 0,
    },
  };

  if (!videoId) {
    return (
      <div className="w-full h-[170px] bg-gray-200 dark:bg-gray-700 rounded-md flex items-center justify-center">
        <p className="text-gray-500 dark:text-gray-400">No video available</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-3">
      <div className="w-full rounded-md overflow-hidden">
        <YouTube 
          videoId={videoId} 
          opts={opts} 
          onReady={onPlayerReady}
          onError={onPlayerError}
          className="w-full"
        />
      </div>
      
      {/* Custom Controls */}
      <div className="flex gap-2 justify-center flex-wrap">
        <button 
          onClick={backwardVideo} 
          disabled={!isReady}
          className="bg-gray-300 hover:bg-gray-400 disabled:bg-gray-200 disabled:cursor-not-allowed px-3 py-1 rounded text-sm font-medium transition-colors"
          title="Rewind 10 seconds"
        >
          ⏪ -10s
        </button>
        <button 
          onClick={playVideo} 
          disabled={!isReady}
          className="bg-green-500 hover:bg-green-600 disabled:bg-green-300 disabled:cursor-not-allowed text-white px-3 py-1 rounded text-sm font-medium transition-colors"
          title="Play"
        >
          ▶️ Play
        </button>
        <button 
          onClick={pauseVideo} 
          disabled={!isReady}
          className="bg-yellow-500 hover:bg-yellow-600 disabled:bg-yellow-300 disabled:cursor-not-allowed text-white px-3 py-1 rounded text-sm font-medium transition-colors"
          title="Pause"
        >
          ⏸ Pause
        </button>
        <button 
          onClick={forwardVideo} 
          disabled={!isReady}
          className="bg-gray-300 hover:bg-gray-400 disabled:bg-gray-200 disabled:cursor-not-allowed px-3 py-1 rounded text-sm font-medium transition-colors"
          title="Forward 10 seconds"
        >
          ⏩ +10s
        </button>
      </div>
    </div>
  );
};

export default YouTubePlayer;
