import React, { useRef, useState, useEffect } from "react";
import YouTube from "react-youtube";
import { YOUTUBE_CONFIG } from "../config/youtube";

const YouTubePlayer = ({ videoId, title = "YouTube Video" }) => {
  const playerRef = useRef(null);
  const [isReady, setIsReady] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [volume, setVolume] = useState(50);
  const [videoInfo, setVideoInfo] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch video information using YouTube API
  useEffect(() => {
    const fetchVideoInfo = async () => {
      if (!videoId) {
        setIsLoading(false);
        return;
      }

      try {
        const response = await fetch(
          `${YOUTUBE_CONFIG.API_BASE_URL}/videos?part=snippet,statistics&id=${videoId}&key=${YOUTUBE_CONFIG.API_KEY}`
        );
        const data = await response.json();

        if (data.items && data.items.length > 0) {
          setVideoInfo(data.items[0]);
        }
      } catch (error) {
        console.error('Error fetching video info:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchVideoInfo();
  }, [videoId]);

  const onPlayerReady = (event) => {
    playerRef.current = event.target;
    setIsReady(true);
    setIsLoading(false);
    // Set initial volume
    if (playerRef.current) {
      playerRef.current.setVolume(volume);
    }
  };

  const onPlayerError = (event) => {
    console.error('YouTube Player Error:', event.data);
    setIsLoading(false);
  };

  const onPlayerStateChange = (event) => {
    // YouTube player states: -1 (unstarted), 0 (ended), 1 (playing), 2 (paused), 3 (buffering), 5 (cued)
    setIsPlaying(event.data === 1);
  };

  const playVideo = () => {
    if (playerRef.current && isReady) {
      playerRef.current.playVideo();
      setIsPlaying(true);
    }
  };

  const pauseVideo = () => {
    if (playerRef.current && isReady) {
      playerRef.current.pauseVideo();
      setIsPlaying(false);
    }
  };

  const togglePlayPause = () => {
    if (isPlaying) {
      pauseVideo();
    } else {
      playVideo();
    }
  };

  const forwardVideo = () => {
    if (playerRef.current && isReady) {
      const currentTime = playerRef.current.getCurrentTime();
      playerRef.current.seekTo(currentTime + 10, true); // Forward 10 seconds
    }
  };

  const backwardVideo = () => {
    if (playerRef.current && isReady) {
      const currentTime = playerRef.current.getCurrentTime();
      playerRef.current.seekTo(Math.max(0, currentTime - 10), true); // Backward 10 seconds
    }
  };

  const toggleMute = () => {
    if (playerRef.current && isReady) {
      if (isMuted) {
        playerRef.current.unMute();
        setIsMuted(false);
      } else {
        playerRef.current.mute();
        setIsMuted(true);
      }
    }
  };

  const handleVolumeChange = (newVolume) => {
    if (playerRef.current && isReady) {
      playerRef.current.setVolume(newVolume);
      setVolume(newVolume);
      if (newVolume === 0) {
        setIsMuted(true);
      } else if (isMuted) {
        setIsMuted(false);
      }
    }
  };

  const opts = {
    height: "170",
    width: "100%",
    playerVars: {
      autoplay: 0,
      controls: 0, // Disable YouTube's built-in controls
      rel: 0,
      showinfo: 0,
      modestbranding: 1,
      fs: 0, // Disable fullscreen button
      cc_load_policy: 0,
      iv_load_policy: 3,
      autohide: 0,
      disablekb: 1, // Disable keyboard controls
      playsinline: 1,
      origin: window.location.origin
    },
  };

  if (!videoId) {
    return (
      <div className="w-full h-[170px] bg-gray-200 dark:bg-gray-700 rounded-md flex items-center justify-center">
        <p className="text-gray-500 dark:text-gray-400">No video available</p>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="w-full h-[170px] bg-gray-100 dark:bg-gray-800 rounded-md flex items-center justify-center">
        <div className="flex items-center gap-2">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading video...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-3">
      <div className="w-full rounded-md overflow-hidden">
        <YouTube
          videoId={videoId}
          opts={opts}
          onReady={onPlayerReady}
          onError={onPlayerError}
          onStateChange={onPlayerStateChange}
          className="w-full"
        />
      </div>

      {/* Video Information */}
      {videoInfo && (
        <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-md">
          <h4 className="font-semibold text-sm text-gray-800 dark:text-gray-200 mb-1">
            {videoInfo.snippet.title}
          </h4>
          <div className="flex items-center gap-4 text-xs text-gray-600 dark:text-gray-400">
            <span>👁️ {parseInt(videoInfo.statistics.viewCount).toLocaleString()} views</span>
            <span>👍 {parseInt(videoInfo.statistics.likeCount).toLocaleString()} likes</span>
            <span>📅 {new Date(videoInfo.snippet.publishedAt).toLocaleDateString()}</span>
          </div>
        </div>
      )}

      {/* Custom Controls */}
      <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-md">
        {/* Main Control Buttons */}
        <div className="flex gap-2 justify-center flex-wrap mb-3">
          <button
            onClick={backwardVideo}
            disabled={!isReady}
            className="bg-gray-300 hover:bg-gray-400 disabled:bg-gray-200 disabled:cursor-not-allowed px-3 py-1 rounded text-sm font-medium transition-colors"
            title="Rewind 10 seconds"
          >
            ⏪ -10s
          </button>
          <button
            onClick={togglePlayPause}
            disabled={!isReady}
            className={`${isPlaying
              ? 'bg-yellow-500 hover:bg-yellow-600 disabled:bg-yellow-300'
              : 'bg-green-500 hover:bg-green-600 disabled:bg-green-300'
            } disabled:cursor-not-allowed text-white px-4 py-1 rounded text-sm font-medium transition-colors`}
            title={isPlaying ? "Pause" : "Play"}
          >
            {isPlaying ? '⏸ Pause' : '▶️ Play'}
          </button>
          <button
            onClick={forwardVideo}
            disabled={!isReady}
            className="bg-gray-300 hover:bg-gray-400 disabled:bg-gray-200 disabled:cursor-not-allowed px-3 py-1 rounded text-sm font-medium transition-colors"
            title="Forward 10 seconds"
          >
            ⏩ +10s
          </button>
        </div>

        {/* Volume Controls */}
        <div className="flex items-center justify-center gap-3">
          <button
            onClick={toggleMute}
            disabled={!isReady}
            className="bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed text-white px-3 py-1 rounded text-sm font-medium transition-colors"
            title={isMuted ? "Unmute" : "Mute"}
          >
            {isMuted ? '🔇' : '🔊'}
          </button>
          <div className="flex items-center gap-2">
            <span className="text-xs text-gray-600 dark:text-gray-400">0</span>
            <input
              type="range"
              min="0"
              max="100"
              value={volume}
              onChange={(e) => handleVolumeChange(parseInt(e.target.value))}
              disabled={!isReady}
              className="w-20 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700 disabled:cursor-not-allowed"
              title="Volume"
            />
            <span className="text-xs text-gray-600 dark:text-gray-400">100</span>
          </div>
          <span className="text-xs text-gray-600 dark:text-gray-400 min-w-[3rem]">
            {volume}%
          </span>
        </div>
      </div>
    </div>
  );
};

export default YouTubePlayer;
