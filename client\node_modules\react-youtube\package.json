{"name": "react-youtube", "version": "10.1.0", "description": "React.js powered YouTube player component", "main": "dist/YouTube.js", "module": "dist/YouTube.esm.js", "types": "dist/YouTube.d.ts", "sideEffects": false, "files": ["dist/**"], "repository": {"type": "git", "url": "**************:tjallingt/react-youtube.git", "directory": "packages/react-youtube"}, "keywords": ["react", "youtube", "player", "react-component"], "author": "Tja<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/tjallingt/react-youtube/issues"}, "homepage": "https://github.com/tjallingt/react-youtube", "dependencies": {"fast-deep-equal": "3.1.3", "prop-types": "15.8.1", "youtube-player": "5.5.2"}, "devDependencies": {"@testing-library/jest-dom": "5.16.5", "@testing-library/react": "13.4.0", "@types/jest": "29.0.0", "@types/react": "18.0.18", "@types/react-dom": "18.0.6", "@types/youtube-player": "5.5.6", "jest": "29.0.2", "jest-environment-jsdom": "29.0.2", "react": "18.2.0", "react-dom": "18.2.0", "semantic-release": "19.0.5", "semantic-release-monorepo": "7.0.5", "ts-jest": "29.0.0", "tsup": "6.5.0"}, "peerDependencies": {"react": ">=0.14.1"}, "engines": {"node": ">= 14.x"}, "scripts": {"test": "jest", "dev": "tsup src/YouTube.tsx --format esm,cjs --watch --dts --external react", "build": "tsup src/YouTube.tsx --format esm,cjs --dts --external react", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf dist", "release": "semantic-release", "prepublishOnly": "npm run build"}, "jest": {"testEnvironment": "jsdom", "preset": "ts-jest"}}