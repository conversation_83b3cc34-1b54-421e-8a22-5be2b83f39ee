// YouTube API Configuration
export const YOUTUBE_CONFIG = {
  // Your YouTube Data API v3 key
  API_KEY: 'AIzaSyBpv2IAPcXqO9bgHfwmDa-dJemrvzChNgs',
  
  // YouTube API endpoints
  API_BASE_URL: 'https://www.googleapis.com/youtube/v3',
  
  // Default player options
  DEFAULT_PLAYER_OPTS: {
    height: '170',
    width: '100%',
    playerVars: {
      autoplay: 0,
      controls: 1,
      rel: 0,
      showinfo: 0,
      modestbranding: 1,
      fs: 1,
      cc_load_policy: 0,
      iv_load_policy: 3,
      autohide: 0,
    },
  }
};

// Note: In production, you should store the API key in environment variables
// and access it via import.meta.env.VITE_YOUTUBE_API_KEY
